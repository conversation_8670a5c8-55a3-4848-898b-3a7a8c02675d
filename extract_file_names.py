#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件处理脚本 - 提取第二列中?file=后面的文件名
"""

import pandas as pd
import sys
import os
import re
from urllib.parse import unquote

def extract_file_names_from_urls(input_file, output_file=None):
    """
    从Excel文件第二列的URL中提取?file=后面的文件名
    
    Args:
        input_file (str): 输入Excel文件路径
        output_file (str): 输出Excel文件路径，如果为None则覆盖原文件
    
    Returns:
        tuple: (处理的行数, 成功提取的行数, 失败的行数)
    """
    try:
        # 读取Excel文件
        print(f"正在读取文件: {input_file}")
        df = pd.read_excel(input_file)
        
        # 显示原始数据信息
        total_rows = len(df)
        print(f"总行数: {total_rows}")
        print(f"总列数: {len(df.columns)}")
        
        # 检查是否有第二列
        if len(df.columns) < 2:
            print("警告: 文件中列数少于2列，无法处理第二列")
            return total_rows, 0, 0
        
        # 获取第二列
        second_column = df.columns[1]
        print(f"第二列名称: {second_column}")
        
        # 显示前几行原始数据
        print("\n原始数据前3行:")
        for i in range(min(3, len(df))):
            print(f"行{i}: {df.iloc[i, 1]}")
        
        # 统计包含?file=的行数
        contains_file_param = df.iloc[:, 1].astype(str).str.contains(r'\?file=', na=False)
        file_param_count = contains_file_param.sum()
        print(f"\n包含?file=参数的行数: {file_param_count}")
        
        # 处理第二列数据
        processed_count = 0
        failed_count = 0
        
        def extract_file_names(cell_value):
            """从单元格值中提取所有?file=后面的文件名"""
            nonlocal processed_count, failed_count
            
            if pd.isna(cell_value) or not isinstance(cell_value, str):
                return cell_value
            
            # 查找所有?file=参数
            file_pattern = r'\?file=([^,&\s]+)'
            matches = re.findall(file_pattern, cell_value)
            
            if matches:
                processed_count += 1
                # URL解码文件名
                decoded_files = []
                for match in matches:
                    try:
                        # URL解码
                        decoded_file = unquote(match)
                        decoded_files.append(decoded_file)
                    except Exception as e:
                        print(f"解码失败: {match}, 错误: {e}")
                        decoded_files.append(match)
                
                # 用逗号分隔多个文件名
                result = ', '.join(decoded_files)
                return result
            else:
                # 如果没有找到?file=参数，保持原值
                return cell_value
        
        # 应用提取函数到第二列
        print("\n开始处理数据...")
        df.iloc[:, 1] = df.iloc[:, 1].apply(extract_file_names)
        
        print(f"处理完成:")
        print(f"  成功提取的行数: {processed_count}")
        print(f"  失败的行数: {failed_count}")
        
        # 显示处理后的前几行数据
        print("\n处理后数据前3行:")
        for i in range(min(3, len(df))):
            print(f"行{i}: {df.iloc[i, 1]}")
        
        # 确定输出文件名
        if output_file is None:
            # 创建备份文件
            backup_file = input_file.replace('.xlsx', '_backup.xlsx')
            if not os.path.exists(backup_file):
                # 重新读取原文件创建备份
                original_df = pd.read_excel(input_file)
                original_df.to_excel(backup_file, index=False)
                print(f"已创建备份文件: {backup_file}")
            output_file = input_file
        
        # 保存处理后的数据
        df.to_excel(output_file, index=False)
        print(f"\n已保存处理后的文件: {output_file}")
        
        return total_rows, processed_count, failed_count
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        return 0, 0, 0
    except Exception as e:
        print(f"处理文件时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return 0, 0, 0

def main():
    """主函数"""
    # 默认输入文件
    input_file = "不同.xlsx"
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        print("用法: python extract_file_names.py [输入文件路径]")
        return
    
    # 处理文件
    total, processed, failed = extract_file_names_from_urls(input_file)
    
    if processed > 0:
        print(f"\n✅ 处理完成!")
        print(f"   总行数: {total}")
        print(f"   成功提取的行数: {processed}")
        print(f"   失败的行数: {failed}")
        print(f"   处理比例: {processed/total*100:.1f}%")
    elif total > 0:
        print(f"\n✅ 文件检查完成，没有找到包含?file=参数的数据")
    else:
        print(f"\n❌ 处理失败")

if __name__ == "__main__":
    main()
