#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件处理脚本 - 去除第二列为空的数据行
"""

import pandas as pd
import sys
import os

def remove_empty_second_column(input_file, output_file=None):
    """
    去除Excel文件中第二列为空的数据行
    
    Args:
        input_file (str): 输入Excel文件路径
        output_file (str): 输出Excel文件路径，如果为None则覆盖原文件
    
    Returns:
        tuple: (处理前行数, 处理后行数, 删除的行数)
    """
    try:
        # 读取Excel文件
        print(f"正在读取文件: {input_file}")
        df = pd.read_excel(input_file)
        
        # 显示原始数据信息
        original_rows = len(df)
        print(f"原始数据行数: {original_rows}")
        print(f"原始数据列数: {len(df.columns)}")
        
        # 显示前几行数据以便确认
        print("\n原始数据前5行:")
        print(df.head())
        
        # 检查是否有第二列
        if len(df.columns) < 2:
            print("警告: 文件中列数少于2列，无法处理第二列")
            return original_rows, original_rows, 0
        
        # 获取第二列的列名（索引为1）
        second_column = df.columns[1]
        print(f"\n第二列名称: {second_column}")
        
        # 显示第二列的空值情况
        null_count = df[second_column].isnull().sum()
        empty_string_count = (df[second_column] == '').sum()
        print(f"第二列中空值(NaN)数量: {null_count}")
        print(f"第二列中空字符串数量: {empty_string_count}")
        
        # 去除第二列为空的行（包括NaN和空字符串）
        # 使用notna()去除NaN值，同时去除空字符串
        df_cleaned = df[df[second_column].notna() & (df[second_column] != '')]
        
        # 显示处理后的数据信息
        cleaned_rows = len(df_cleaned)
        removed_rows = original_rows - cleaned_rows
        
        print(f"\n处理后数据行数: {cleaned_rows}")
        print(f"删除的行数: {removed_rows}")
        
        # 显示处理后的前几行数据
        print("\n处理后数据前5行:")
        print(df_cleaned.head())
        
        # 确定输出文件名
        if output_file is None:
            # 创建备份文件
            backup_file = input_file.replace('.xlsx', '_backup.xlsx')
            if not os.path.exists(backup_file):
                df.to_excel(backup_file, index=False)
                print(f"已创建备份文件: {backup_file}")
            output_file = input_file
        
        # 保存处理后的数据
        df_cleaned.to_excel(output_file, index=False)
        print(f"\n已保存处理后的文件: {output_file}")
        
        return original_rows, cleaned_rows, removed_rows
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        return 0, 0, 0
    except Exception as e:
        print(f"处理文件时发生错误: {str(e)}")
        return 0, 0, 0

def main():
    """主函数"""
    # 默认输入文件
    input_file = "file_gpt.xlsx"
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 文件 {input_file} 不存在")
        print("用法: python remove_empty_rows.py [输入文件路径]")
        return
    
    # 处理文件
    original, cleaned, removed = remove_empty_second_column(input_file)
    
    if removed > 0:
        print(f"\n✅ 处理完成!")
        print(f"   原始行数: {original}")
        print(f"   处理后行数: {cleaned}")
        print(f"   删除行数: {removed}")
        print(f"   删除比例: {removed/original*100:.1f}%")
    elif original > 0:
        print(f"\n✅ 文件检查完成，第二列没有空值，无需处理")
    else:
        print(f"\n❌ 处理失败")

if __name__ == "__main__":
    main()
