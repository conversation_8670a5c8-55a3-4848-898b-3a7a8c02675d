from openai import OpenAI
import csv
import time

# 设置API密钥
client = OpenAI(api_key="632f1176-9dd3-4c36-955d-dfba0e52cf51", base_url="https://ark.cn-beijing.volces.com/api/v3")

# 固定的提示词
PROMPT_TEMPLATE = """我们的产品型号中可能数字间会带有"-"。例如"TDB08-C"这种，他实际上与"TDB08C"是一个产品。同时，可能有简写，例如"ID700ABC" 这个代表是三款产品 "ID700-A" "ID700-B" "ID700-C"。那么你需要比对这三款是否有不存在的。

请比对以下产品型号：
查询产品: {query_product}

现有产品列表:
{existing_products}

请分析查询产品是否在现有产品列表中存在。
 OutputFormat: JSON格式，包含查询产品是否存在的判断结果。
 Examples:
  - 例子1：
    查询产品: "TDB08-C,TDB08-D"
    现有产品列表: ["TDB08C", "ID700-A", "ID700-B"]
    输出结果: {"TDB08-C": true, "TDB08-D": false}
  - 例子2：
    查询产品: "ID700ABC,ID700-ABC"
    现有产品列表: ["ID700-A", "ID700-B", "ID700-C"]
    输出结果: {"ID700ABC": true, "ID700-ABC": true}
  - 例子3：
    查询产品: "TDB06[GM],TDB08"
    现有产品列表: ["TDB06", "GKL-70L", "GKL-70"]
    输出结果: {"TDB06[GM]": true, "TDB08": false}
  - 例子4：
    查询产品: "GKL-70L/GKL-70,GKL-80L"
    现有产品列表: ["GKL-70L", "GKL-70", "GKL-80L/GKL-90"]
    输出结果: {"GKL-70L/GKL-70": true, "GKL-80L": true}
  - 例子5：
    查询产品: "XYZ123"
    现有产品列表: ["TDB08C", "ID700-A", "ID700-B"]
    输出结果: {"XYZ123": false}
"""

def read_file_lines(filename):
    """读取文件的所有行，去除重复和空行"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]
            return list(set(lines))  # 去重
    except FileNotFoundError:
        print(f"文件 {filename} 不存在")
        return []

def call_openai_api(query_product, existing_products):
    """调用OpenAI API进行产品比对"""
    try:
        # 构建提示词
        existing_products_str = '\n'.join(existing_products)
        prompt = PROMPT_TEMPLATE.format(
            query_product=query_product,
            existing_products=existing_products_str
        )

        # 发送对话请求
        response = client.chat.completions.create(
            model="deepseek-v3-250324",
            messages=[{"role": "user", "content": prompt}]
        )

        return response.choices[0].message.content.strip()
    except Exception as e:
        print(f"API调用失败: {e}")
        return "API调用失败"

def main():
    # 读取文件
    txt_data = read_file_lines('不同.txt')
    different_data = read_file_lines('fastgpt.txt')

    if not txt_data:
        print("txt.txt 文件为空或不存在")
        return

    if not different_data:
        print("不同.txt 文件为空或不存在")
        return

    print(f"从 txt.txt 读取到 {len(txt_data)} 条数据")
    print(f"从 不同.txt 读取到 {len(different_data)} 条数据")

    # 存储不存在的产品
    non_existing_products = []

    # 处理每条数据
    for i, product in enumerate(txt_data, 1):
        print(f"正在处理第 {i}/{len(txt_data)} 条数据: {product}")

        # 调用API进行比对
        result = call_openai_api(product, different_data)
        print(f"API返回结果: {result}")

        # 判断是否不存在
        if "不存在" in result:
            non_existing_products.append({
                'product': product,
                'api_result': result
            })
            print(f"发现不存在的产品: {product}")

        # 添加延迟避免API限制
        time.sleep(1)

    # 将结果写入CSV文件
    output_filename = 'non_existing_products.csv'
    with open(output_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        fieldnames = ['product', 'api_result']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        # 写入表头
        writer.writeheader()

        # 写入数据
        for item in non_existing_products:
            writer.writerow(item)

    print(f"\n处理完成！")
    print(f"总共处理了 {len(txt_data)} 条数据")
    print(f"发现 {len(non_existing_products)} 条不存在的产品")
    print(f"结果已保存到 {output_filename}")

if __name__ == "__main__":
    main()
